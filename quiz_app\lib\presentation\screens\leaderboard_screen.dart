import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/entities/quiz_result.dart';
import '../providers/quiz_providers.dart';
import '../widgets/animated_background.dart';

class LeaderboardScreen extends ConsumerWidget {
  const LeaderboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final quizResults = ref.watch(quizResultsProvider);
    final theme = Theme.of(context);

    // Sort results by score (descending)
    final sortedResults = List<QuizResult>.from(quizResults)
      ..sort((a, b) => b.score.compareTo(a.score));

    return AnimatedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('Tableau des Scores'),
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: sortedResults.isEmpty
                ? _buildEmptyState(theme)
                : _buildLeaderboard(sortedResults, theme),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.emoji_events_outlined,
            size: 80,
            color: theme.colorScheme.onSurface.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Aucun score enregistré',
            style: theme.textTheme.headlineSmall?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Commencez un quiz pour voir vos scores ici',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withOpacity(0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLeaderboard(List<QuizResult> results, ThemeData theme) {
    return Column(
      children: [
        // Top 3 podium
        if (results.isNotEmpty) _buildPodium(results.take(3).toList(), theme),
        
        const SizedBox(height: 24),
        
        // All results list
        Expanded(
          child: ListView.builder(
            itemCount: results.length,
            itemBuilder: (context, index) {
              return _buildResultCard(results[index], index + 1, theme);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPodium(List<QuizResult> topResults, ThemeData theme) {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // 2nd place
          if (topResults.length > 1)
            _buildPodiumPlace(topResults[1], 2, 120, theme),
          
          // 1st place
          _buildPodiumPlace(topResults[0], 1, 150, theme),
          
          // 3rd place
          if (topResults.length > 2)
            _buildPodiumPlace(topResults[2], 3, 100, theme),
        ],
      ),
    );
  }

  Widget _buildPodiumPlace(QuizResult result, int place, double height, ThemeData theme) {
    final colors = [
      const Color(0xFFFFD700), // Gold
      const Color(0xFFC0C0C0), // Silver
      const Color(0xFFCD7F32), // Bronze
    ];
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // Trophy icon
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            color: colors[place - 1],
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.emoji_events,
            color: Colors.white,
            size: 30,
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Score
        Text(
          '${result.score}',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        // Category
        Text(
          result.category,
          style: theme.textTheme.bodySmall,
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        
        const SizedBox(height: 8),
        
        // Podium base
        Container(
          width: 80,
          height: height,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                colors[place - 1].withOpacity(0.8),
                colors[place - 1],
              ],
            ),
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(8),
              topRight: Radius.circular(8),
            ),
          ),
          child: Center(
            child: Text(
              '$place',
              style: theme.textTheme.headlineLarge?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildResultCard(QuizResult result, int rank, ThemeData theme) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: rank <= 3 
              ? [const Color(0xFFFFD700), const Color(0xFFC0C0C0), const Color(0xFFCD7F32)][rank - 1]
              : theme.colorScheme.primary,
          child: Text(
            '$rank',
            style: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          '${result.score} points',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${result.category} • ${result.difficulty}'),
            Text(
              '${result.correctAnswers}/${result.totalQuestions} correct • ${result.percentage.toStringAsFixed(1)}%',
            ),
            Text(
              _formatDate(result.completedAt),
              style: theme.textTheme.bodySmall,
            ),
          ],
        ),
        trailing: Icon(
          result.percentage >= 80 
              ? Icons.star 
              : result.percentage >= 60 
                  ? Icons.thumb_up 
                  : Icons.trending_up,
          color: result.percentage >= 80 
              ? Colors.amber 
              : result.percentage >= 60 
                  ? Colors.green 
                  : Colors.orange,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
