import 'package:flutter/material.dart';
import 'dart:math' as math;

class AnimatedBackground extends StatefulWidget {
  final Widget child;

  const AnimatedBackground({
    super.key,
    required this.child,
  });

  @override
  State<AnimatedBackground> createState() => _AnimatedBackgroundState();
}

class _AnimatedBackgroundState extends State<AnimatedBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller1;
  late AnimationController _controller2;
  late AnimationController _controller3;

  @override
  void initState() {
    super.initState();
    
    _controller1 = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
    
    _controller2 = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    )..repeat();
    
    _controller3 = AnimationController(
      duration: const Duration(seconds: 25),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller1.dispose();
    _controller2.dispose();
    _controller3.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      body: Stack(
        children: [
          // Background gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  theme.colorScheme.background,
                  theme.colorScheme.surface,
                ],
              ),
            ),
          ),
          
          // Animated circles
          AnimatedBuilder(
            animation: Listenable.merge([_controller1, _controller2, _controller3]),
            builder: (context, child) {
              return Stack(
                children: [
                  // Circle 1
                  Positioned(
                    top: -100 + (math.sin(_controller1.value * 2 * math.pi) * 50),
                    left: -100 + (math.cos(_controller1.value * 2 * math.pi) * 30),
                    child: Container(
                      width: 200,
                      height: 200,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            theme.colorScheme.primary.withOpacity(0.1),
                            theme.colorScheme.primary.withOpacity(0.05),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                  ),
                  
                  // Circle 2
                  Positioned(
                    top: MediaQuery.of(context).size.height * 0.3 +
                        (math.sin(_controller2.value * 2 * math.pi) * 40),
                    right: -150 + (math.cos(_controller2.value * 2 * math.pi) * 60),
                    child: Container(
                      width: 300,
                      height: 300,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            theme.colorScheme.secondary.withOpacity(0.08),
                            theme.colorScheme.secondary.withOpacity(0.04),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                  ),
                  
                  // Circle 3
                  Positioned(
                    bottom: -150 + (math.sin(_controller3.value * 2 * math.pi) * 30),
                    left: MediaQuery.of(context).size.width * 0.2 +
                        (math.cos(_controller3.value * 2 * math.pi) * 40),
                    child: Container(
                      width: 250,
                      height: 250,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            theme.colorScheme.tertiary.withOpacity(0.06),
                            theme.colorScheme.tertiary.withOpacity(0.03),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
          
          // Content
          widget.child,
        ],
      ),
    );
  }
}
