import 'package:equatable/equatable.dart';

class QuizResult extends Equatable {
  final int score;
  final int totalQuestions;
  final int correctAnswers;
  final int incorrectAnswers;
  final String category;
  final String difficulty;
  final Duration timeTaken;
  final DateTime completedAt;
  final List<QuestionResult> questionResults;

  const QuizResult({
    required this.score,
    required this.totalQuestions,
    required this.correctAnswers,
    required this.incorrectAnswers,
    required this.category,
    required this.difficulty,
    required this.timeTaken,
    required this.completedAt,
    required this.questionResults,
  });

  double get percentage => (correctAnswers / totalQuestions) * 100;

  @override
  List<Object?> get props => [
        score,
        totalQuestions,
        correctAnswers,
        incorrectAnswers,
        category,
        difficulty,
        timeTaken,
        completedAt,
        questionResults,
      ];

  QuizResult copyWith({
    int? score,
    int? totalQuestions,
    int? correctAnswers,
    int? incorrectAnswers,
    String? category,
    String? difficulty,
    Duration? timeTaken,
    DateTime? completedAt,
    List<QuestionResult>? questionResults,
  }) {
    return QuizResult(
      score: score ?? this.score,
      totalQuestions: totalQuestions ?? this.totalQuestions,
      correctAnswers: correctAnswers ?? this.correctAnswers,
      incorrectAnswers: incorrectAnswers ?? this.incorrectAnswers,
      category: category ?? this.category,
      difficulty: difficulty ?? this.difficulty,
      timeTaken: timeTaken ?? this.timeTaken,
      completedAt: completedAt ?? this.completedAt,
      questionResults: questionResults ?? this.questionResults,
    );
  }
}

class QuestionResult extends Equatable {
  final String question;
  final String correctAnswer;
  final String? userAnswer;
  final bool isCorrect;
  final Duration timeSpent;
  final int pointsEarned;

  const QuestionResult({
    required this.question,
    required this.correctAnswer,
    this.userAnswer,
    required this.isCorrect,
    required this.timeSpent,
    required this.pointsEarned,
  });

  @override
  List<Object?> get props => [
        question,
        correctAnswer,
        userAnswer,
        isCorrect,
        timeSpent,
        pointsEarned,
      ];

  QuestionResult copyWith({
    String? question,
    String? correctAnswer,
    String? userAnswer,
    bool? isCorrect,
    Duration? timeSpent,
    int? pointsEarned,
  }) {
    return QuestionResult(
      question: question ?? this.question,
      correctAnswer: correctAnswer ?? this.correctAnswer,
      userAnswer: userAnswer ?? this.userAnswer,
      isCorrect: isCorrect ?? this.isCorrect,
      timeSpent: timeSpent ?? this.timeSpent,
      pointsEarned: pointsEarned ?? this.pointsEarned,
    );
  }
}
