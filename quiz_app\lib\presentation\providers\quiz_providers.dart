import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/network/dio_client.dart';
import '../../core/storage/hive_service.dart';
import '../../data/datasources/quiz_remote_datasource.dart';
import '../../data/datasources/quiz_local_datasource.dart';
import '../../data/repositories/quiz_repository_impl.dart';
import '../../domain/repositories/quiz_repository.dart';
import '../../domain/usecases/get_categories.dart';
import '../../domain/usecases/get_questions.dart';
import '../../domain/usecases/save_quiz_result.dart';
import '../../domain/usecases/get_quiz_results.dart';
import '../../domain/entities/category.dart';
import '../../domain/entities/question.dart';
import '../../domain/entities/quiz_result.dart';

// Core providers
final dioClientProvider = Provider<DioClient>((ref) => DioClient.instance);
final hiveServiceProvider = Provider<HiveService>((ref) => HiveService.instance);

// Data source providers
final quizRemoteDataSourceProvider = Provider<QuizRemoteDataSource>((ref) {
  return QuizRemoteDataSourceImpl(ref.read(dioClientProvider));
});

final quizLocalDataSourceProvider = Provider<QuizLocalDataSource>((ref) {
  return QuizLocalDataSourceImpl(ref.read(hiveServiceProvider));
});

// Repository provider
final quizRepositoryProvider = Provider<QuizRepository>((ref) {
  return QuizRepositoryImpl(
    ref.read(quizRemoteDataSourceProvider),
    ref.read(quizLocalDataSourceProvider),
  );
});

// Use case providers
final getCategoriesProvider = Provider<GetCategories>((ref) {
  return GetCategories(ref.read(quizRepositoryProvider));
});

final getQuestionsProvider = Provider<GetQuestions>((ref) {
  return GetQuestions(ref.read(quizRepositoryProvider));
});

final saveQuizResultProvider = Provider<SaveQuizResult>((ref) {
  return SaveQuizResult(ref.read(quizRepositoryProvider));
});

final getQuizResultsProvider = Provider<GetQuizResults>((ref) {
  return GetQuizResults(ref.read(quizRepositoryProvider));
});

final getBestScoreProvider = Provider<GetBestScore>((ref) {
  return GetBestScore(ref.read(quizRepositoryProvider));
});

// State providers
final categoriesProvider = FutureProvider<List<Category>>((ref) async {
  final getCategories = ref.read(getCategoriesProvider);
  return await getCategories();
});

final questionsProvider = FutureProvider.family<List<Question>, GetQuestionsParams>((ref, params) async {
  final getQuestions = ref.read(getQuestionsProvider);
  return await getQuestions(params);
});

final quizResultsProvider = Provider<List<QuizResult>>((ref) {
  final getQuizResults = ref.read(getQuizResultsProvider);
  return getQuizResults();
});

final bestScoreProvider = Provider<QuizResult?>((ref) {
  final getBestScore = ref.read(getBestScoreProvider);
  return getBestScore();
});

// Settings providers
final themeProvider = StateNotifierProvider<ThemeNotifier, String>((ref) {
  return ThemeNotifier(ref.read(quizRepositoryProvider));
});

final languageProvider = StateNotifierProvider<LanguageNotifier, String>((ref) {
  return LanguageNotifier(ref.read(quizRepositoryProvider));
});

final soundEnabledProvider = StateNotifierProvider<SoundNotifier, bool>((ref) {
  return SoundNotifier(ref.read(quizRepositoryProvider));
});

final vibrationEnabledProvider = StateNotifierProvider<VibrationNotifier, bool>((ref) {
  return VibrationNotifier(ref.read(quizRepositoryProvider));
});

// State Notifiers
class ThemeNotifier extends StateNotifier<String> {
  final QuizRepository _repository;

  ThemeNotifier(this._repository) : super('system') {
    _loadTheme();
  }

  void _loadTheme() {
    state = _repository.getThemeMode();
  }

  Future<void> setTheme(String theme) async {
    await _repository.setThemeMode(theme);
    state = theme;
  }
}

class LanguageNotifier extends StateNotifier<String> {
  final QuizRepository _repository;

  LanguageNotifier(this._repository) : super('en') {
    _loadLanguage();
  }

  void _loadLanguage() {
    state = _repository.getLanguage();
  }

  Future<void> setLanguage(String language) async {
    await _repository.setLanguage(language);
    state = language;
  }
}

class SoundNotifier extends StateNotifier<bool> {
  final QuizRepository _repository;

  SoundNotifier(this._repository) : super(true) {
    _loadSoundSetting();
  }

  void _loadSoundSetting() {
    state = _repository.getSoundEnabled();
  }

  Future<void> setSoundEnabled(bool enabled) async {
    await _repository.setSoundEnabled(enabled);
    state = enabled;
  }
}

class VibrationNotifier extends StateNotifier<bool> {
  final QuizRepository _repository;

  VibrationNotifier(this._repository) : super(true) {
    _loadVibrationSetting();
  }

  void _loadVibrationSetting() {
    state = _repository.getVibrationEnabled();
  }

  Future<void> setVibrationEnabled(bool enabled) async {
    await _repository.setVibrationEnabled(enabled);
    state = enabled;
  }
}
