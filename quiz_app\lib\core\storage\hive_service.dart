import 'package:hive_flutter/hive_flutter.dart';
import '../constants/app_constants.dart';
import '../../domain/entities/quiz_result.dart';

class HiveService {
  static HiveService? _instance;

  HiveService._internal();

  static HiveService get instance {
    _instance ??= HiveService._internal();
    return _instance!;
  }

  // Initialize Hive
  Future<void> init() async {
    await Hive.initFlutter();

    // Register adapters
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(QuizResultAdapter());
    }
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(QuestionResultAdapter());
    }

    // Open boxes
    await _openBoxes();
  }

  Future<void> _openBoxes() async {
    await Hive.openBox<QuizResult>(AppConstants.userScoresBox);
    await Hive.openBox(AppConstants.settingsBox);
  }

  // Settings methods
  Future<void> setThemeMode(String themeMode) async {
    final box = Hive.box(AppConstants.settingsBox);
    await box.put(AppConstants.themeKey, themeMode);
  }

  String getThemeMode() {
    final box = Hive.box(AppConstants.settingsBox);
    return box.get(AppConstants.themeKey, defaultValue: 'system');
  }

  Future<void> setLanguage(String language) async {
    final box = Hive.box(AppConstants.settingsBox);
    await box.put(AppConstants.languageKey, language);
  }

  String getLanguage() {
    final box = Hive.box(AppConstants.settingsBox);
    return box.get(AppConstants.languageKey, defaultValue: 'en');
  }

  Future<void> setSoundEnabled(bool enabled) async {
    final box = Hive.box(AppConstants.settingsBox);
    await box.put(AppConstants.soundEnabledKey, enabled);
  }

  bool getSoundEnabled() {
    final box = Hive.box(AppConstants.settingsBox);
    return box.get(AppConstants.soundEnabledKey, defaultValue: true);
  }

  Future<void> setVibrationEnabled(bool enabled) async {
    final box = Hive.box(AppConstants.settingsBox);
    await box.put(AppConstants.vibrationEnabledKey, enabled);
  }

  bool getVibrationEnabled() {
    final box = Hive.box(AppConstants.settingsBox);
    return box.get(AppConstants.vibrationEnabledKey, defaultValue: true);
  }

  Future<void> setNotificationsEnabled(bool enabled) async {
    final box = Hive.box(AppConstants.settingsBox);
    await box.put(AppConstants.notificationsEnabledKey, enabled);
  }

  bool getNotificationsEnabled() {
    final box = Hive.box(AppConstants.settingsBox);
    return box.get(AppConstants.notificationsEnabledKey, defaultValue: true);
  }

  // Quiz results methods
  Future<void> saveQuizResult(QuizResult result) async {
    final box = Hive.box<QuizResult>(AppConstants.userScoresBox);
    await box.add(result);
  }

  List<QuizResult> getAllQuizResults() {
    final box = Hive.box<QuizResult>(AppConstants.userScoresBox);
    return box.values.toList();
  }

  List<QuizResult> getQuizResultsByCategory(String category) {
    final box = Hive.box<QuizResult>(AppConstants.userScoresBox);
    return box.values.where((result) => result.category == category).toList();
  }

  List<QuizResult> getQuizResultsByDifficulty(String difficulty) {
    final box = Hive.box<QuizResult>(AppConstants.userScoresBox);
    return box.values.where((result) => result.difficulty == difficulty).toList();
  }

  QuizResult? getBestScore() {
    final results = getAllQuizResults();
    if (results.isEmpty) return null;

    results.sort((a, b) => b.score.compareTo(a.score));
    return results.first;
  }

  QuizResult? getBestScoreByCategory(String category) {
    final results = getQuizResultsByCategory(category);
    if (results.isEmpty) return null;

    results.sort((a, b) => b.score.compareTo(a.score));
    return results.first;
  }

  Future<void> clearAllScores() async {
    final box = Hive.box<QuizResult>(AppConstants.userScoresBox);
    await box.clear();
  }

  Future<void> clearAllSettings() async {
    final box = Hive.box(AppConstants.settingsBox);
    await box.clear();
  }

  // Close all boxes
  Future<void> close() async {
    await Hive.close();
  }
}

// Hive Adapters
class QuizResultAdapter extends TypeAdapter<QuizResult> {
  @override
  final int typeId = 0;

  @override
  QuizResult read(BinaryReader reader) {
    return QuizResult(
      score: reader.readInt(),
      totalQuestions: reader.readInt(),
      correctAnswers: reader.readInt(),
      incorrectAnswers: reader.readInt(),
      category: reader.readString(),
      difficulty: reader.readString(),
      timeTaken: Duration(milliseconds: reader.readInt()),
      completedAt: DateTime.fromMillisecondsSinceEpoch(reader.readInt()),
      questionResults: reader.readList().cast<QuestionResult>(),
    );
  }

  @override
  void write(BinaryWriter writer, QuizResult obj) {
    writer.writeInt(obj.score);
    writer.writeInt(obj.totalQuestions);
    writer.writeInt(obj.correctAnswers);
    writer.writeInt(obj.incorrectAnswers);
    writer.writeString(obj.category);
    writer.writeString(obj.difficulty);
    writer.writeInt(obj.timeTaken.inMilliseconds);
    writer.writeInt(obj.completedAt.millisecondsSinceEpoch);
    writer.writeList(obj.questionResults);
  }
}

class QuestionResultAdapter extends TypeAdapter<QuestionResult> {
  @override
  final int typeId = 1;

  @override
  QuestionResult read(BinaryReader reader) {
    return QuestionResult(
      question: reader.readString(),
      correctAnswer: reader.readString(),
      userAnswer: reader.readString(),
      isCorrect: reader.readBool(),
      timeSpent: Duration(milliseconds: reader.readInt()),
      pointsEarned: reader.readInt(),
    );
  }

  @override
  void write(BinaryWriter writer, QuestionResult obj) {
    writer.writeString(obj.question);
    writer.writeString(obj.correctAnswer);
    writer.writeString(obj.userAnswer ?? '');
    writer.writeBool(obj.isCorrect);
    writer.writeInt(obj.timeSpent.inMilliseconds);
    writer.writeInt(obj.pointsEarned);
  }
}
