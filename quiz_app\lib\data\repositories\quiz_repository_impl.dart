import '../../domain/entities/category.dart';
import '../../domain/entities/question.dart';
import '../../domain/entities/quiz_result.dart';
import '../../domain/repositories/quiz_repository.dart';
import '../datasources/quiz_remote_datasource.dart';
import '../datasources/quiz_local_datasource.dart';

class QuizRepositoryImpl implements QuizRepository {
  final QuizRemoteDataSource _remoteDataSource;
  final QuizLocalDataSource _localDataSource;

  QuizRepositoryImpl(this._remoteDataSource, this._localDataSource);

  @override
  Future<List<Category>> getCategories() async {
    try {
      final categoryModels = await _remoteDataSource.getCategories();
      return categoryModels.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw Exception('Failed to get categories: $e');
    }
  }

  @override
  Future<List<Question>> getQuestions({
    required int amount,
    int? category,
    String? difficulty,
    String? type,
  }) async {
    try {
      final questionModels = await _remoteDataSource.getQuestions(
        amount: amount,
        category: category,
        difficulty: difficulty,
        type: type,
      );
      return questionModels.map((model) => model.toEntity()).toList();
    } catch (e) {
      throw Exception('Failed to get questions: $e');
    }
  }

  @override
  Future<void> saveQuizResult(QuizResult result) async {
    try {
      await _localDataSource.saveQuizResult(result);
    } catch (e) {
      throw Exception('Failed to save quiz result: $e');
    }
  }

  @override
  List<QuizResult> getAllQuizResults() {
    try {
      return _localDataSource.getAllQuizResults();
    } catch (e) {
      throw Exception('Failed to get quiz results: $e');
    }
  }

  @override
  List<QuizResult> getQuizResultsByCategory(String category) {
    try {
      return _localDataSource.getQuizResultsByCategory(category);
    } catch (e) {
      throw Exception('Failed to get quiz results by category: $e');
    }
  }

  @override
  List<QuizResult> getQuizResultsByDifficulty(String difficulty) {
    try {
      return _localDataSource.getQuizResultsByDifficulty(difficulty);
    } catch (e) {
      throw Exception('Failed to get quiz results by difficulty: $e');
    }
  }

  @override
  QuizResult? getBestScore() {
    try {
      return _localDataSource.getBestScore();
    } catch (e) {
      throw Exception('Failed to get best score: $e');
    }
  }

  @override
  QuizResult? getBestScoreByCategory(String category) {
    try {
      return _localDataSource.getBestScoreByCategory(category);
    } catch (e) {
      throw Exception('Failed to get best score by category: $e');
    }
  }

  @override
  Future<void> clearAllScores() async {
    try {
      await _localDataSource.clearAllScores();
    } catch (e) {
      throw Exception('Failed to clear scores: $e');
    }
  }

  // Settings methods
  @override
  Future<void> setThemeMode(String themeMode) async {
    try {
      await _localDataSource.setThemeMode(themeMode);
    } catch (e) {
      throw Exception('Failed to set theme mode: $e');
    }
  }

  @override
  String getThemeMode() {
    try {
      return _localDataSource.getThemeMode();
    } catch (e) {
      throw Exception('Failed to get theme mode: $e');
    }
  }

  @override
  Future<void> setLanguage(String language) async {
    try {
      await _localDataSource.setLanguage(language);
    } catch (e) {
      throw Exception('Failed to set language: $e');
    }
  }

  @override
  String getLanguage() {
    try {
      return _localDataSource.getLanguage();
    } catch (e) {
      throw Exception('Failed to get language: $e');
    }
  }

  @override
  Future<void> setSoundEnabled(bool enabled) async {
    try {
      await _localDataSource.setSoundEnabled(enabled);
    } catch (e) {
      throw Exception('Failed to set sound enabled: $e');
    }
  }

  @override
  bool getSoundEnabled() {
    try {
      return _localDataSource.getSoundEnabled();
    } catch (e) {
      throw Exception('Failed to get sound enabled: $e');
    }
  }

  @override
  Future<void> setVibrationEnabled(bool enabled) async {
    try {
      await _localDataSource.setVibrationEnabled(enabled);
    } catch (e) {
      throw Exception('Failed to set vibration enabled: $e');
    }
  }

  @override
  bool getVibrationEnabled() {
    try {
      return _localDataSource.getVibrationEnabled();
    } catch (e) {
      throw Exception('Failed to get vibration enabled: $e');
    }
  }

  @override
  Future<void> setNotificationsEnabled(bool enabled) async {
    try {
      await _localDataSource.setNotificationsEnabled(enabled);
    } catch (e) {
      throw Exception('Failed to set notifications enabled: $e');
    }
  }

  @override
  bool getNotificationsEnabled() {
    try {
      return _localDataSource.getNotificationsEnabled();
    } catch (e) {
      throw Exception('Failed to get notifications enabled: $e');
    }
  }
}
