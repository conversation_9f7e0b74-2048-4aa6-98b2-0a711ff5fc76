import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/audio_manager.dart';
import '../../domain/usecases/get_questions.dart';
import '../providers/quiz_providers.dart';
import '../widgets/custom_button.dart';
import '../widgets/animated_background.dart';
import 'quiz_screen.dart';

class QuizSetupScreen extends ConsumerStatefulWidget {
  const QuizSetupScreen({super.key});

  @override
  ConsumerState<QuizSetupScreen> createState() => _QuizSetupScreenState();
}

class _QuizSetupScreenState extends ConsumerState<QuizSetupScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  int _selectedCategoryId = 0; // 0 means any category
  String _selectedDifficulty = '';
  String _selectedType = '';
  int _questionCount = AppConstants.defaultQuestionCount;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: AppConstants.longAnimation,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _startQuiz() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    AudioManager.instance.buttonPressFeedback();

    try {
      final params = GetQuestionsParams(
        amount: _questionCount,
        category: _selectedCategoryId == 0 ? null : _selectedCategoryId,
        difficulty: _selectedDifficulty.isEmpty ? null : _selectedDifficulty,
        type: _selectedType.isEmpty ? null : _selectedType,
      );

      // Navigate to quiz screen
      if (mounted) {
        Navigator.of(context).push(
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) =>
                QuizScreen(params: params),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return SlideTransition(
                position: Tween<Offset>(
                  begin: const Offset(1.0, 0.0),
                  end: Offset.zero,
                ).animate(animation),
                child: child,
              );
            },
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final categoriesAsync = ref.watch(categoriesProvider);

    return AnimatedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('Configuration du Quiz'),
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Number of questions
                            _buildSectionTitle('Nombre de questions'),
                            _buildQuestionCountSlider(),
                            
                            const SizedBox(height: 24),
                            
                            // Category selection
                            _buildSectionTitle('Catégorie'),
                            categoriesAsync.when(
                              data: (categories) => _buildCategoryDropdown(categories),
                              loading: () => const Center(child: CircularProgressIndicator()),
                              error: (error, stack) => Text('Erreur: $error'),
                            ),
                            
                            const SizedBox(height: 24),
                            
                            // Difficulty selection
                            _buildSectionTitle('Difficulté'),
                            _buildDifficultySelection(),
                            
                            const SizedBox(height: 24),
                            
                            // Type selection
                            _buildSectionTitle('Type de questions'),
                            _buildTypeSelection(),
                          ],
                        ),
                      ),
                    ),
                    
                    // Start button
                    const SizedBox(height: 24),
                    CustomButton(
                      text: 'Commencer le Quiz',
                      icon: Icons.play_arrow,
                      onPressed: _startQuiz,
                      isPrimary: true,
                      isLoading: _isLoading,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildQuestionCountSlider() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text('Questions:'),
                Text(
                  '$_questionCount',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            Slider(
              value: _questionCount.toDouble(),
              min: 5,
              max: AppConstants.maxQuestionCount.toDouble(),
              divisions: 9,
              onChanged: (value) {
                setState(() {
                  _questionCount = value.round();
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryDropdown(categories) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: DropdownButtonFormField<int>(
          value: _selectedCategoryId,
          decoration: const InputDecoration(
            border: InputBorder.none,
            hintText: 'Sélectionner une catégorie',
          ),
          items: [
            const DropdownMenuItem(
              value: 0,
              child: Text('Toutes les catégories'),
            ),
            ...categories.map<DropdownMenuItem<int>>((category) {
              return DropdownMenuItem(
                value: category.id,
                child: Text(category.name),
              );
            }).toList(),
          ],
          onChanged: (value) {
            setState(() {
              _selectedCategoryId = value ?? 0;
            });
          },
        ),
      ),
    );
  }

  Widget _buildDifficultySelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: QuizDifficulty.values.map((difficulty) {
            return RadioListTile<String>(
              title: Text(difficulty.displayName),
              value: difficulty.value,
              groupValue: _selectedDifficulty,
              onChanged: (value) {
                setState(() {
                  _selectedDifficulty = value ?? '';
                });
              },
            );
          }).toList()
            ..insert(
              0,
              RadioListTile<String>(
                title: const Text('Toutes les difficultés'),
                value: '',
                groupValue: _selectedDifficulty,
                onChanged: (value) {
                  setState(() {
                    _selectedDifficulty = value ?? '';
                  });
                },
              ),
            ),
        ),
      ),
    );
  }

  Widget _buildTypeSelection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: QuestionType.values.map((type) {
            return RadioListTile<String>(
              title: Text(type.displayName),
              value: type.value,
              groupValue: _selectedType,
              onChanged: (value) {
                setState(() {
                  _selectedType = value ?? '';
                });
              },
            );
          }).toList()
            ..insert(
              0,
              RadioListTile<String>(
                title: const Text('Tous les types'),
                value: '',
                groupValue: _selectedType,
                onChanged: (value) {
                  setState(() {
                    _selectedType = value ?? '';
                  });
                },
              ),
            ),
        ),
      ),
    );
  }
}
