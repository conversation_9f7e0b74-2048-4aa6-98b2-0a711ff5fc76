import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:liquid_progress_indicator/liquid_progress_indicator.dart';
import '../../core/constants/app_constants.dart';
import '../../core/utils/audio_manager.dart';
import '../../domain/entities/question.dart';
import '../../domain/entities/quiz_result.dart';
import '../../domain/usecases/get_questions.dart';
import '../providers/quiz_providers.dart';
import '../widgets/animated_background.dart';
import '../widgets/custom_button.dart';
import 'results_screen.dart';
import 'dart:async';

class QuizScreen extends ConsumerStatefulWidget {
  final GetQuestionsParams params;

  const QuizScreen({
    super.key,
    required this.params,
  });

  @override
  ConsumerState<QuizScreen> createState() => _QuizScreenState();
}

class _QuizScreenState extends ConsumerState<QuizScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _questionController;
  late Animation<double> _questionAnimation;
  
  Timer? _timer;
  int _currentQuestionIndex = 0;
  int _timeRemaining = AppConstants.questionTimeLimit;
  int _score = 0;
  List<QuestionResult> _questionResults = [];
  List<Question> _questions = [];
  String? _selectedAnswer;
  bool _isAnswered = false;
  bool _isLoading = true;
  DateTime _questionStartTime = DateTime.now();

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadQuestions();
  }

  void _initializeAnimations() {
    _progressController = AnimationController(
      duration: Duration(seconds: AppConstants.questionTimeLimit),
      vsync: this,
    );

    _questionController = AnimationController(
      duration: AppConstants.mediumAnimation,
      vsync: this,
    );

    _questionAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _questionController,
      curve: Curves.easeInOut,
    ));
  }

  void _loadQuestions() async {
    try {
      final questions = await ref.read(getQuestionsProvider)(widget.params);
      if (mounted) {
        setState(() {
          _questions = questions;
          _isLoading = false;
        });
        _startQuestion();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
        Navigator.of(context).pop();
      }
    }
  }

  void _startQuestion() {
    _questionStartTime = DateTime.now();
    _timeRemaining = AppConstants.questionTimeLimit;
    _selectedAnswer = null;
    _isAnswered = false;
    
    _questionController.forward();
    _progressController.forward();
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _timeRemaining--;
        });
        
        if (_timeRemaining <= 0) {
          _timeUp();
        }
      }
    });
  }

  void _timeUp() {
    if (!_isAnswered) {
      _answerQuestion(null);
    }
  }

  void _answerQuestion(String? answer) {
    if (_isAnswered) return;
    
    setState(() {
      _selectedAnswer = answer;
      _isAnswered = true;
    });
    
    _timer?.cancel();
    _progressController.stop();
    
    final currentQuestion = _questions[_currentQuestionIndex];
    final isCorrect = answer == currentQuestion.correctAnswer;
    final timeSpent = DateTime.now().difference(_questionStartTime);
    
    // Calculate points
    int points = 0;
    if (isCorrect) {
      points = AppConstants.correctAnswerPoints;
      // Time bonus
      points += (_timeRemaining * AppConstants.timeBonus);
      _score += points;
      
      AudioManager.instance.correctAnswerFeedback();
    } else {
      AudioManager.instance.wrongAnswerFeedback();
    }
    
    // Save question result
    _questionResults.add(QuestionResult(
      question: currentQuestion.question,
      correctAnswer: currentQuestion.correctAnswer,
      userAnswer: answer,
      isCorrect: isCorrect,
      timeSpent: timeSpent,
      pointsEarned: points,
    ));
    
    // Show feedback for 2 seconds, then move to next question
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _nextQuestion();
      }
    });
  }

  void _nextQuestion() {
    if (_currentQuestionIndex < _questions.length - 1) {
      setState(() {
        _currentQuestionIndex++;
      });
      
      _questionController.reset();
      _progressController.reset();
      _startQuestion();
    } else {
      _finishQuiz();
    }
  }

  void _finishQuiz() async {
    AudioManager.instance.playGameOver();
    
    final quizResult = QuizResult(
      score: _score,
      totalQuestions: _questions.length,
      correctAnswers: _questionResults.where((r) => r.isCorrect).length,
      incorrectAnswers: _questionResults.where((r) => !r.isCorrect).length,
      category: _getCategoryName(),
      difficulty: widget.params.difficulty ?? 'Mixed',
      timeTaken: DateTime.now().difference(_questionStartTime),
      completedAt: DateTime.now(),
      questionResults: _questionResults,
    );
    
    // Save result
    try {
      await ref.read(saveQuizResultProvider)(quizResult);
    } catch (e) {
      // Handle error silently or show a message
      print('Error saving quiz result: $e');
    }
    
    // Navigate to results
    if (mounted) {
      Navigator.of(context).pushReplacement(
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) =>
              ResultsScreen(result: quizResult),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
        ),
      );
    }
  }

  String _getCategoryName() {
    if (widget.params.category == null) return 'Mixed';
    // You might want to get the actual category name from the categories list
    return 'Category ${widget.params.category}';
  }

  @override
  void dispose() {
    _timer?.cancel();
    _progressController.dispose();
    _questionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingScreen();
    }

    if (_questions.isEmpty) {
      return _buildErrorScreen();
    }

    return _buildQuizScreen();
  }

  Widget _buildLoadingScreen() {
    return AnimatedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('Chargement des questions...'),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorScreen() {
    return AnimatedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.error_outline, size: 64),
              const SizedBox(height: 16),
              const Text('Aucune question trouvée'),
              const SizedBox(height: 16),
              CustomButton(
                text: 'Retour',
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuizScreen() {
    final theme = Theme.of(context);
    final currentQuestion = _questions[_currentQuestionIndex];
    final progress = (_currentQuestionIndex + 1) / _questions.length;

    return AnimatedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text('Question ${_currentQuestionIndex + 1}/${_questions.length}'),
          actions: [
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: Center(
                child: Text(
                  'Score: $_score',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                // Progress indicators
                _buildProgressSection(progress, theme),
                
                const SizedBox(height: 24),
                
                // Question section
                Expanded(
                  child: FadeTransition(
                    opacity: _questionAnimation,
                    child: _buildQuestionSection(currentQuestion, theme),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildProgressSection(double progress, ThemeData theme) {
    return Column(
      children: [
        // Overall progress
        Row(
          children: [
            Expanded(
              child: LinearProgressIndicator(
                value: progress,
                backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
                valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
              ),
            ),
            const SizedBox(width: 16),
            Text(
              '${(_currentQuestionIndex + 1)}/${_questions.length}',
              style: theme.textTheme.titleSmall,
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Time progress
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: 8,
                child: LiquidLinearProgressIndicator(
                  value: _timeRemaining / AppConstants.questionTimeLimit,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _timeRemaining > 10 
                        ? theme.colorScheme.primary
                        : theme.colorScheme.error,
                  ),
                  backgroundColor: theme.colorScheme.outline.withOpacity(0.2),
                  borderRadius: 4,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              decoration: BoxDecoration(
                color: _timeRemaining > 10 
                    ? theme.colorScheme.primary
                    : theme.colorScheme.error,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '$_timeRemaining s',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuestionSection(Question question, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Question
        Card(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Question',
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: theme.colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  question.question,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 24),
        
        // Answers
        Expanded(
          child: ListView.builder(
            itemCount: question.allAnswers.length,
            itemBuilder: (context, index) {
              final answer = question.allAnswers[index];
              return _buildAnswerOption(answer, question.correctAnswer, theme);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAnswerOption(String answer, String correctAnswer, ThemeData theme) {
    Color? backgroundColor;
    Color? textColor;
    IconData? icon;
    
    if (_isAnswered) {
      if (answer == correctAnswer) {
        backgroundColor = Colors.green;
        textColor = Colors.white;
        icon = Icons.check_circle;
      } else if (answer == _selectedAnswer) {
        backgroundColor = Colors.red;
        textColor = Colors.white;
        icon = Icons.cancel;
      }
    } else if (answer == _selectedAnswer) {
      backgroundColor = theme.colorScheme.primary.withOpacity(0.1);
      textColor = theme.colorScheme.primary;
    }
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Material(
        color: backgroundColor ?? theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        elevation: 2,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: _isAnswered ? null : () => _answerQuestion(answer),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    answer,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: textColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                if (icon != null) ...[
                  const SizedBox(width: 12),
                  Icon(icon, color: textColor),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
