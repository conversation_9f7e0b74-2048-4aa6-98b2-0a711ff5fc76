import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/quiz_providers.dart';
import '../widgets/animated_background.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final themeMode = ref.watch(themeProvider);
    final language = ref.watch(languageProvider);
    final soundEnabled = ref.watch(soundEnabledProvider);
    final vibrationEnabled = ref.watch(vibrationEnabledProvider);

    return AnimatedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: const Text('Paramètres'),
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        body: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Expanded(
                  child: <PERSON><PERSON>iew(
                    children: [
                      // Appearance section
                      _buildSectionHeader('Apparence', theme),
                      _buildThemeCard(context, ref, themeMode, theme),
                      _buildLanguageCard(context, ref, language, theme),
                      
                      const SizedBox(height: 24),
                      
                      // Audio & Feedback section
                      _buildSectionHeader('Audio et Retour', theme),
                      _buildSoundCard(ref, soundEnabled, theme),
                      _buildVibrationCard(ref, vibrationEnabled, theme),
                      
                      const SizedBox(height: 24),
                      
                      // Data section
                      _buildSectionHeader('Données', theme),
                      _buildClearDataCard(context, ref, theme),
                      
                      const SizedBox(height: 24),
                      
                      // About section
                      _buildSectionHeader('À propos', theme),
                      _buildAboutCard(context, theme),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12, top: 8),
      child: Text(
        title,
        style: theme.textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildThemeCard(BuildContext context, WidgetRef ref, String currentTheme, ThemeData theme) {
    return Card(
      child: ListTile(
        leading: Icon(
          Icons.palette_outlined,
          color: theme.colorScheme.primary,
        ),
        title: const Text('Thème'),
        subtitle: Text(_getThemeDisplayName(currentTheme)),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showThemeDialog(context, ref, currentTheme),
      ),
    );
  }

  Widget _buildLanguageCard(BuildContext context, WidgetRef ref, String currentLanguage, ThemeData theme) {
    return Card(
      child: ListTile(
        leading: Icon(
          Icons.language,
          color: theme.colorScheme.primary,
        ),
        title: const Text('Langue'),
        subtitle: Text(_getLanguageDisplayName(currentLanguage)),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showLanguageDialog(context, ref, currentLanguage),
      ),
    );
  }

  Widget _buildSoundCard(WidgetRef ref, bool soundEnabled, ThemeData theme) {
    return Card(
      child: SwitchListTile(
        secondary: Icon(
          soundEnabled ? Icons.volume_up : Icons.volume_off,
          color: theme.colorScheme.primary,
        ),
        title: const Text('Sons'),
        subtitle: const Text('Activer les effets sonores'),
        value: soundEnabled,
        onChanged: (value) {
          ref.read(soundEnabledProvider.notifier).setSoundEnabled(value);
        },
      ),
    );
  }

  Widget _buildVibrationCard(WidgetRef ref, bool vibrationEnabled, ThemeData theme) {
    return Card(
      child: SwitchListTile(
        secondary: Icon(
          vibrationEnabled ? Icons.vibration : Icons.phone_android,
          color: theme.colorScheme.primary,
        ),
        title: const Text('Vibrations'),
        subtitle: const Text('Activer les vibrations'),
        value: vibrationEnabled,
        onChanged: (value) {
          ref.read(vibrationEnabledProvider.notifier).setVibrationEnabled(value);
        },
      ),
    );
  }

  Widget _buildClearDataCard(BuildContext context, WidgetRef ref, ThemeData theme) {
    return Card(
      child: ListTile(
        leading: Icon(
          Icons.delete_outline,
          color: theme.colorScheme.error,
        ),
        title: Text(
          'Effacer les données',
          style: TextStyle(color: theme.colorScheme.error),
        ),
        subtitle: const Text('Supprimer tous les scores enregistrés'),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showClearDataDialog(context, ref),
      ),
    );
  }

  Widget _buildAboutCard(BuildContext context, ThemeData theme) {
    return Card(
      child: ListTile(
        leading: Icon(
          Icons.info_outline,
          color: theme.colorScheme.primary,
        ),
        title: const Text('À propos de l\'application'),
        subtitle: const Text('Version, crédits et informations'),
        trailing: const Icon(Icons.chevron_right),
        onTap: () => _showAboutDialog(context),
      ),
    );
  }

  String _getThemeDisplayName(String theme) {
    switch (theme) {
      case 'light':
        return 'Clair';
      case 'dark':
        return 'Sombre';
      case 'system':
      default:
        return 'Système';
    }
  }

  String _getLanguageDisplayName(String language) {
    switch (language) {
      case 'fr':
        return 'Français';
      case 'en':
        return 'English';
      case 'ar':
        return 'العربية';
      default:
        return 'English';
    }
  }

  void _showThemeDialog(BuildContext context, WidgetRef ref, String currentTheme) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choisir le thème'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('Système'),
              value: 'system',
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  ref.read(themeProvider.notifier).setTheme(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('Clair'),
              value: 'light',
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  ref.read(themeProvider.notifier).setTheme(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('Sombre'),
              value: 'dark',
              groupValue: currentTheme,
              onChanged: (value) {
                if (value != null) {
                  ref.read(themeProvider.notifier).setTheme(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showLanguageDialog(BuildContext context, WidgetRef ref, String currentLanguage) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choisir la langue'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            RadioListTile<String>(
              title: const Text('English'),
              value: 'en',
              groupValue: currentLanguage,
              onChanged: (value) {
                if (value != null) {
                  ref.read(languageProvider.notifier).setLanguage(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('Français'),
              value: 'fr',
              groupValue: currentLanguage,
              onChanged: (value) {
                if (value != null) {
                  ref.read(languageProvider.notifier).setLanguage(value);
                  Navigator.of(context).pop();
                }
              },
            ),
            RadioListTile<String>(
              title: const Text('العربية'),
              value: 'ar',
              groupValue: currentLanguage,
              onChanged: (value) {
                if (value != null) {
                  ref.read(languageProvider.notifier).setLanguage(value);
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showClearDataDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Effacer les données'),
        content: const Text(
          'Êtes-vous sûr de vouloir supprimer tous vos scores ? '
          'Cette action est irréversible.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () async {
              try {
                await ref.read(quizRepositoryProvider).clearAllScores();
                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Données supprimées avec succès'),
                    ),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Erreur: ${e.toString()}'),
                      backgroundColor: Theme.of(context).colorScheme.error,
                    ),
                  );
                }
              }
            },
            child: Text(
              'Supprimer',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'Quiz Master',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          borderRadius: BorderRadius.circular(15),
        ),
        child: const Icon(
          Icons.quiz,
          color: Colors.white,
          size: 30,
        ),
      ),
      children: [
        const Text(
          'Une application de quiz avancée développée avec Flutter. '
          'Testez vos connaissances dans différentes catégories et difficultés.',
        ),
        const SizedBox(height: 16),
        const Text('Fonctionnalités:'),
        const Text('• Questions de l\'Open Trivia Database'),
        const Text('• Thèmes clair et sombre'),
        const Text('• Support multilingue'),
        const Text('• Effets sonores et vibrations'),
        const Text('• Sauvegarde des scores'),
        const SizedBox(height: 16),
        const Text('Développé avec ❤️ en Flutter'),
      ],
    );
  }
}
