# Quiz Master - Application Flutter de Quiz Avancé

Une application mobile de quiz sophistiquée développée avec Flutter, utilisant l'architecture MVVM et les meilleures pratiques de développement.

## 🚀 Fonctionnalités

### 🎯 Quiz Interactif
- Questions provenant de l'Open Trivia Database
- Sélection de catégories et difficultés
- Chronomètre visuel avec indicateur de progression liquide
- Feedback immédiat avec animations et sons
- Système de scoring avancé avec bonus de temps

### 🎨 Interface Utilisateur
- Design Material 3 moderne
- Thèmes clair et sombre
- Animations fluides et micro-interactions
- Interface responsive et intuitive

### 🌍 Internationalisation
- Support multilingue (FR/EN/AR)
- Interface adaptée selon la langue sélectionnée

### 🔊 Audio & Retour Haptique
- Effets sonores pour les interactions
- Vibrations pour le feedback tactile
- Contrôles de volume et paramètres audio

### 📊 Statistiques & Classements
- Sauvegarde des scores avec Hive
- Tableau de bord avec graphiques
- Historique des performances
- Classements par catégorie et difficulté

### ⚙️ Paramètres Avancés
- Gestion des thèmes
- Paramètres audio et vibrations
- Gestion des langues
- Réinitialisation des données

## 🏗️ Architecture

### Clean Architecture + MVVM
```
lib/
├── core/                 # Utilitaires et services partagés
│   ├── constants/       # Constantes de l'application
│   ├── network/         # Client HTTP (Dio)
│   ├── storage/         # Service de stockage (Hive)
│   ├── themes/          # Thèmes Material 3
│   └── utils/           # Gestionnaire audio et utilitaires
├── data/                # Couche de données
│   ├── datasources/     # Sources de données (API, local)
│   ├── models/          # Modèles de données
│   └── repositories/    # Implémentations des repositories
├── domain/              # Logique métier
│   ├── entities/        # Entités métier
│   ├── repositories/    # Interfaces des repositories
│   └── usecases/        # Cas d'usage
└── presentation/        # Interface utilisateur
    ├── providers/       # Gestion d'état (Riverpod)
    ├── screens/         # Écrans de l'application
    └── widgets/         # Widgets réutilisables
```

## 📦 Dépendances Principales

### State Management & Architecture
- `flutter_riverpod` - Gestion d'état réactive
- `equatable` - Comparaison d'objets

### Network & API
- `dio` - Client HTTP avancé
- `connectivity_plus` - Vérification de connectivité

### Stockage Local
- `hive` & `hive_flutter` - Base de données NoSQL rapide
- `path_provider` - Accès aux répertoires système

### UI & Animations
- `animations` - Animations avancées
- `lottie` - Animations Lottie
- `liquid_progress_indicator` - Indicateurs de progression liquides
- `smooth_page_indicator` - Indicateurs de page fluides
- `fl_chart` - Graphiques et charts

### Internationalisation
- `easy_localization` - Gestion multilingue

### Audio & Feedback
- `just_audio` - Lecture audio
- `vibration` - Contrôle des vibrations

### Firebase & Notifications
- `firebase_core` & `firebase_messaging` - Notifications push
- `flutter_local_notifications` - Notifications locales

## 🛠️ Installation et Configuration

### Prérequis
- Flutter SDK (>=3.7.2)
- Dart SDK
- Android Studio / VS Code
- Émulateur Android ou appareil physique

### Installation
1. Cloner le repository
```bash
git clone <repository-url>
cd quiz_app
```

2. Installer les dépendances
```bash
flutter pub get
```

3. Générer les fichiers Hive (si nécessaire)
```bash
flutter packages pub run build_runner build
```

4. Lancer l'application
```bash
flutter run
```

## 🔧 Configuration Firebase (Optionnel)

1. Créer un projet Firebase
2. Ajouter les fichiers de configuration :
   - `android/app/google-services.json`
   - `ios/Runner/GoogleService-Info.plist`
3. Configurer les notifications push

## 🧪 Tests

### Tests Unitaires
```bash
flutter test
```

### Tests d'Intégration
```bash
flutter test integration_test/
```

## 📱 Captures d'Écran

[Ajouter des captures d'écran de l'application]

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add some AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 👨‍💻 Développeur

Développé avec ❤️ en Flutter

---

**Note**: Cette application utilise l'API gratuite d'Open Trivia Database. Aucune clé API n'est requise.
