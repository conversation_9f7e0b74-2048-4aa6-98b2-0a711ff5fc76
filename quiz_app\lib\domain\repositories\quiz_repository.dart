import '../entities/category.dart';
import '../entities/question.dart';
import '../entities/quiz_result.dart';

abstract class QuizRepository {
  // Remote operations
  Future<List<Category>> getCategories();
  Future<List<Question>> getQuestions({
    required int amount,
    int? category,
    String? difficulty,
    String? type,
  });

  // Local operations - Quiz Results
  Future<void> saveQuizResult(QuizResult result);
  List<QuizResult> getAllQuizResults();
  List<QuizResult> getQuizResultsByCategory(String category);
  List<QuizResult> getQuizResultsByDifficulty(String difficulty);
  QuizResult? getBestScore();
  QuizResult? getBestScoreByCategory(String category);
  Future<void> clearAllScores();

  // Local operations - Settings
  Future<void> setThemeMode(String themeMode);
  String getThemeMode();
  Future<void> setLanguage(String language);
  String getLanguage();
  Future<void> setSoundEnabled(bool enabled);
  bool getSoundEnabled();
  Future<void> setVibrationEnabled(bool enabled);
  bool getVibrationEnabled();
  Future<void> setNotificationsEnabled(bool enabled);
  bool getNotificationsEnabled();
}
