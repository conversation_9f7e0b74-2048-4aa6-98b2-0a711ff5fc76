import 'package:flutter_test/flutter_test.dart';
import 'package:quiz_app/domain/entities/quiz_result.dart';

void main() {
  group('QuizResult', () {
    test('should calculate percentage correctly', () {
      // Arrange
      final quizResult = QuizResult(
        score: 80,
        totalQuestions: 10,
        correctAnswers: 8,
        incorrectAnswers: 2,
        category: 'Science',
        difficulty: 'medium',
        timeTaken: Duration(minutes: 5),
        completedAt: DateTime.now(),
        questionResults: [],
      );

      // Act & Assert
      expect(quizResult.percentage, 80.0);
    });

    test('should create QuizResult with all properties', () {
      // Arrange
      final now = DateTime.now();
      final duration = Duration(minutes: 3, seconds: 30);
      final questionResults = [
        QuestionResult(
          question: 'Test question?',
          correctAnswer: 'Correct',
          userAnswer: 'Correct',
          isCorrect: true,
          timeSpent: Duration(seconds: 15),
          pointsEarned: 10,
        ),
      ];

      // Act
      final quizResult = QuizResult(
        score: 100,
        totalQuestions: 5,
        correctAnswers: 5,
        incorrectAnswers: 0,
        category: 'Science',
        difficulty: 'hard',
        timeTaken: duration,
        completedAt: now,
        questionResults: questionResults,
      );

      // Assert
      expect(quizResult.score, 100);
      expect(quizResult.totalQuestions, 5);
      expect(quizResult.correctAnswers, 5);
      expect(quizResult.incorrectAnswers, 0);
      expect(quizResult.category, 'Science');
      expect(quizResult.difficulty, 'hard');
      expect(quizResult.timeTaken, duration);
      expect(quizResult.completedAt, now);
      expect(quizResult.questionResults, questionResults);
      expect(quizResult.percentage, 100.0);
    });

    test('should support equality comparison', () {
      // Arrange
      final now = DateTime.now();
      final duration = Duration(minutes: 2);
      
      final quizResult1 = QuizResult(
        score: 50,
        totalQuestions: 5,
        correctAnswers: 3,
        incorrectAnswers: 2,
        category: 'History',
        difficulty: 'easy',
        timeTaken: duration,
        completedAt: now,
        questionResults: [],
      );

      final quizResult2 = QuizResult(
        score: 50,
        totalQuestions: 5,
        correctAnswers: 3,
        incorrectAnswers: 2,
        category: 'History',
        difficulty: 'easy',
        timeTaken: duration,
        completedAt: now,
        questionResults: [],
      );

      // Act & Assert
      expect(quizResult1, equals(quizResult2));
    });
  });

  group('QuestionResult', () {
    test('should create QuestionResult with all properties', () {
      // Arrange
      final timeSpent = Duration(seconds: 20);

      // Act
      final questionResult = QuestionResult(
        question: 'What is 2+2?',
        correctAnswer: '4',
        userAnswer: '4',
        isCorrect: true,
        timeSpent: timeSpent,
        pointsEarned: 15,
      );

      // Assert
      expect(questionResult.question, 'What is 2+2?');
      expect(questionResult.correctAnswer, '4');
      expect(questionResult.userAnswer, '4');
      expect(questionResult.isCorrect, true);
      expect(questionResult.timeSpent, timeSpent);
      expect(questionResult.pointsEarned, 15);
    });

    test('should handle null user answer', () {
      // Arrange & Act
      final questionResult = QuestionResult(
        question: 'What is 2+2?',
        correctAnswer: '4',
        userAnswer: null,
        isCorrect: false,
        timeSpent: Duration(seconds: 30),
        pointsEarned: 0,
      );

      // Assert
      expect(questionResult.userAnswer, null);
      expect(questionResult.isCorrect, false);
      expect(questionResult.pointsEarned, 0);
    });

    test('should support equality comparison', () {
      // Arrange
      final timeSpent = Duration(seconds: 15);
      
      final questionResult1 = QuestionResult(
        question: 'Test question?',
        correctAnswer: 'Answer',
        userAnswer: 'Answer',
        isCorrect: true,
        timeSpent: timeSpent,
        pointsEarned: 10,
      );

      final questionResult2 = QuestionResult(
        question: 'Test question?',
        correctAnswer: 'Answer',
        userAnswer: 'Answer',
        isCorrect: true,
        timeSpent: timeSpent,
        pointsEarned: 10,
      );

      // Act & Assert
      expect(questionResult1, equals(questionResult2));
    });
  });
}
