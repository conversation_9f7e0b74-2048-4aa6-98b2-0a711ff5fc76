name: quiz_app
description: "Advanced Flutter Quiz App with MVVM Architecture"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # State Management
  flutter_riverpod: ^2.4.9

  # Network & API
  dio: ^5.4.0
  connectivity_plus: ^5.0.2

  # Local Storage (temporarily disabled)
  # hive: ^2.2.3
  # hive_flutter: ^1.1.0
  # path_provider: ^2.1.2

  # UI & Animations
  animations: ^2.0.8
  lottie: ^2.7.0
  smooth_page_indicator: ^1.1.0
  fl_chart: ^0.66.0
  percent_indicator: ^4.2.3

  # Internationalization
  easy_localization: ^3.0.3

  # Audio & Vibration (temporarily disabled for web)
  # just_audio: ^0.9.36
  # vibration: ^1.8.4

  # Notifications (temporarily disabled)
  # firebase_messaging: ^14.7.10
  # flutter_local_notifications: ^16.3.2

  # Firebase Core (temporarily disabled)
  # firebase_core: ^2.24.2

  # Icons
  cupertino_icons: ^1.0.8

  # Utilities
  equatable: ^2.0.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  build_runner: ^2.4.7
  hive_generator: ^2.0.1

  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/animations/
    - assets/audio/
    - assets/translations/

  # Fonts will be added later
  # fonts:
  #   - family: Poppins
  #     fonts:
  #       - asset: assets/fonts/Poppins-Regular.ttf


