import 'package:dio/dio.dart';
import '../../core/network/dio_client.dart';
import '../../core/constants/app_constants.dart';
import '../models/question_model.dart';
import '../models/category_model.dart';

abstract class QuizRemoteDataSource {
  Future<List<CategoryModel>> getCategories();
  Future<List<QuestionModel>> getQuestions({
    required int amount,
    int? category,
    String? difficulty,
    String? type,
  });
}

class QuizRemoteDataSourceImpl implements QuizRemoteDataSource {
  final DioClient _dioClient;

  QuizRemoteDataSourceImpl(this._dioClient);

  @override
  Future<List<CategoryModel>> getCategories() async {
    try {
      final response = await _dioClient.get(AppConstants.categoriesUrl);
      
      if (response.statusCode == 200) {
        final data = response.data;
        if (data['trivia_categories'] != null) {
          final List<dynamic> categoriesJson = data['trivia_categories'];
          return categoriesJson
              .map((json) => CategoryModel.fromJson(json))
              .toList();
        }
      }
      
      throw Exception('Failed to load categories');
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  @override
  Future<List<QuestionModel>> getQuestions({
    required int amount,
    int? category,
    String? difficulty,
    String? type,
  }) async {
    try {
      final queryParameters = <String, dynamic>{
        'amount': amount,
      };

      if (category != null) {
        queryParameters['category'] = category;
      }

      if (difficulty != null && difficulty.isNotEmpty) {
        queryParameters['difficulty'] = difficulty;
      }

      if (type != null && type.isNotEmpty) {
        queryParameters['type'] = type;
      }

      final response = await _dioClient.get(
        AppConstants.baseUrl,
        queryParameters: queryParameters,
      );

      if (response.statusCode == 200) {
        final data = response.data;
        
        // Check response code from API
        final responseCode = data['response_code'];
        if (responseCode == 0) {
          final List<dynamic> questionsJson = data['results'] ?? [];
          return questionsJson
              .map((json) => QuestionModel.fromJson(json))
              .toList();
        } else {
          throw Exception(_getErrorMessage(responseCode));
        }
      }
      
      throw Exception('Failed to load questions');
    } on DioException catch (e) {
      throw Exception('Network error: ${e.message}');
    } catch (e) {
      throw Exception('Unexpected error: $e');
    }
  }

  String _getErrorMessage(int responseCode) {
    switch (responseCode) {
      case 1:
        return 'No results found. Try adjusting your search criteria.';
      case 2:
        return 'Invalid parameter. Please check your request.';
      case 3:
        return 'Token not found. Please try again.';
      case 4:
        return 'Token empty. Please reset your session.';
      case 5:
        return 'Rate limit exceeded. Please wait before making another request.';
      default:
        return 'Unknown error occurred.';
    }
  }
}
