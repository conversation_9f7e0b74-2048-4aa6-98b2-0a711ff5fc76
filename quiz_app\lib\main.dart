import 'package:flutter/material.dart';

void main() {
  runApp(const QuizApp());
}

class QuizApp extends StatelessWidget {
  const QuizApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Quiz Master',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(seedColor: const Color(0xFF6C63FF)),
      ),
      darkTheme: ThemeData(
        useMaterial3: true,
        brightness: Brightness.dark,
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF6C63FF),
          brightness: Brightness.dark,
        ),
      ),
      themeMode: ThemeMode.system,
      home: const DemoHomeScreen(),
    );
  }
}

class DemoHomeScreen extends StatelessWidget {
  const DemoHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              theme.colorScheme.primary,
              theme.colorScheme.secondary,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24.0),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                minHeight: MediaQuery.of(context).size.height - 48,
              ),
              child: Column(
                children: [
                  // Header
                  SizedBox(
                    height: MediaQuery.of(context).size.height * 0.4,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                      // Logo
                      Container(
                        width: 100,
                        height: 100,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.quiz,
                          size: 50,
                          color: Color(0xFF6C63FF),
                        ),
                      ),

                      const SizedBox(height: 20),

                      // App name
                      Text(
                        'Quiz Master',
                        style: theme.textTheme.displayMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Tagline
                      Text(
                        'Testez vos connaissances',
                        style: theme.textTheme.titleLarge?.copyWith(
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ],
                  ),
                ),

                // Menu buttons
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.5,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildMenuButton(
                        context,
                        'Commencer le Quiz',
                        Icons.play_arrow,
                        () => _showComingSoon(context),
                        isPrimary: true,
                      ),

                      const SizedBox(height: 16),

                      _buildMenuButton(
                        context,
                        'Tableau des Scores',
                        Icons.leaderboard,
                        () => _showComingSoon(context),
                      ),

                      const SizedBox(height: 16),

                      _buildMenuButton(
                        context,
                        'Paramètres',
                        Icons.settings,
                        () => _showComingSoon(context),
                      ),

                      const SizedBox(height: 16),

                      _buildMenuButton(
                        context,
                        'À propos',
                        Icons.info_outline,
                        () => _showAbout(context),
                      ),
                    ],
                  ),
                ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMenuButton(
    BuildContext context,
    String text,
    IconData icon,
    VoidCallback onPressed, {
    bool isPrimary = false,
  }) {
    final theme = Theme.of(context);

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon),
        label: Text(text),
        style: ElevatedButton.styleFrom(
          backgroundColor: isPrimary ? Colors.white : theme.colorScheme.surface,
          foregroundColor: isPrimary ? theme.colorScheme.primary : theme.colorScheme.onSurface,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: isPrimary ? 8 : 2,
        ),
      ),
    );
  }

  void _showComingSoon(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bientôt disponible'),
        content: const Text(
          'Cette fonctionnalité sera disponible dans la version complète de l\'application. '
          'L\'architecture MVVM avec Riverpod, Firebase, et Hive est déjà implémentée !',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAbout(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'Quiz Master',
      applicationVersion: '1.0.0',
      applicationIcon: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          borderRadius: BorderRadius.circular(15),
        ),
        child: const Icon(
          Icons.quiz,
          color: Colors.white,
          size: 30,
        ),
      ),
      children: const [
        Text(
          'Une application de quiz avancée développée avec Flutter. '
          'Architecture Clean + MVVM, Riverpod, Firebase, Material 3.',
        ),
        SizedBox(height: 16),
        Text('Fonctionnalités implémentées:'),
        Text('• Architecture Clean + MVVM complète'),
        Text('• Gestion d\'état avec Riverpod'),
        Text('• Intégration Firebase'),
        Text('• Stockage local avec Hive'),
        Text('• UI Material 3 avec thèmes'),
        Text('• Tests unitaires'),
        SizedBox(height: 16),
        Text('Développé avec ❤️ en Flutter'),
      ],
    );
  }
}


