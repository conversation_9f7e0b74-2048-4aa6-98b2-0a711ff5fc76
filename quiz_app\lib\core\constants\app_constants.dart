class AppConstants {
  // API Constants
  static const String baseUrl = 'https://opentdb.com/api.php';
  static const String categoriesUrl = 'https://opentdb.com/api_category.php';
  
  // App Info
  static const String appName = 'Quiz Master';
  static const String appVersion = '1.0.0';
  
  // Storage Keys
  static const String userScoresBox = 'user_scores';
  static const String settingsBox = 'settings';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language';
  static const String soundEnabledKey = 'sound_enabled';
  static const String vibrationEnabledKey = 'vibration_enabled';
  static const String notificationsEnabledKey = 'notifications_enabled';
  
  // Quiz Settings
  static const int defaultQuestionCount = 10;
  static const int maxQuestionCount = 50;
  static const int questionTimeLimit = 30; // seconds
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 300);
  static const Duration mediumAnimation = Duration(milliseconds: 500);
  static const Duration longAnimation = Duration(milliseconds: 800);
  
  // Audio Assets
  static const String correctAnswerSound = 'assets/audio/correct.mp3';
  static const String wrongAnswerSound = 'assets/audio/wrong.mp3';
  static const String buttonClickSound = 'assets/audio/click.mp3';
  static const String gameOverSound = 'assets/audio/game_over.mp3';
  
  // Image Assets
  static const String logoPath = 'assets/images/logo.png';
  static const String splashBackground = 'assets/images/splash_bg.png';
  
  // Lottie Animations
  static const String loadingAnimation = 'assets/animations/loading.json';
  static const String successAnimation = 'assets/animations/success.json';
  static const String failAnimation = 'assets/animations/fail.json';
  static const String trophyAnimation = 'assets/animations/trophy.json';
  
  // Scoring
  static const int correctAnswerPoints = 10;
  static const int timeBonus = 5; // bonus per second remaining
  static const int streakMultiplier = 2;
}

enum QuizDifficulty {
  easy('easy', 'Facile'),
  medium('medium', 'Moyen'),
  hard('hard', 'Difficile');
  
  const QuizDifficulty(this.value, this.displayName);
  final String value;
  final String displayName;
}

enum QuestionType {
  multiple('multiple', 'Choix multiple'),
  boolean('boolean', 'Vrai/Faux');
  
  const QuestionType(this.value, this.displayName);
  final String value;
  final String displayName;
}
