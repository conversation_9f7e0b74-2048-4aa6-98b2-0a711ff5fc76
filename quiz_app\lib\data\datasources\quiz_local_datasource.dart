import '../../core/storage/hive_service.dart';
import '../../domain/entities/quiz_result.dart';

abstract class QuizLocalDataSource {
  Future<void> saveQuizResult(QuizResult result);
  List<QuizResult> getAllQuizResults();
  List<QuizResult> getQuizResultsByCategory(String category);
  List<QuizResult> getQuizResultsByDifficulty(String difficulty);
  QuizResult? getBestScore();
  QuizResult? getBestScoreByCategory(String category);
  Future<void> clearAllScores();
  
  // Settings
  Future<void> setThemeMode(String themeMode);
  String getThemeMode();
  Future<void> setLanguage(String language);
  String getLanguage();
  Future<void> setSoundEnabled(bool enabled);
  bool getSoundEnabled();
  Future<void> setVibrationEnabled(bool enabled);
  bool getVibrationEnabled();
  Future<void> setNotificationsEnabled(bool enabled);
  bool getNotificationsEnabled();
}

class QuizLocalDataSourceImpl implements QuizLocalDataSource {
  final HiveService _hiveService;

  QuizLocalDataSourceImpl(this._hiveService);

  @override
  Future<void> saveQuizResult(QuizResult result) async {
    await _hiveService.saveQuizResult(result);
  }

  @override
  List<QuizResult> getAllQuizResults() {
    return _hiveService.getAllQuizResults();
  }

  @override
  List<QuizResult> getQuizResultsByCategory(String category) {
    return _hiveService.getQuizResultsByCategory(category);
  }

  @override
  List<QuizResult> getQuizResultsByDifficulty(String difficulty) {
    return _hiveService.getQuizResultsByDifficulty(difficulty);
  }

  @override
  QuizResult? getBestScore() {
    return _hiveService.getBestScore();
  }

  @override
  QuizResult? getBestScoreByCategory(String category) {
    return _hiveService.getBestScoreByCategory(category);
  }

  @override
  Future<void> clearAllScores() async {
    await _hiveService.clearAllScores();
  }

  // Settings methods
  @override
  Future<void> setThemeMode(String themeMode) async {
    await _hiveService.setThemeMode(themeMode);
  }

  @override
  String getThemeMode() {
    return _hiveService.getThemeMode();
  }

  @override
  Future<void> setLanguage(String language) async {
    await _hiveService.setLanguage(language);
  }

  @override
  String getLanguage() {
    return _hiveService.getLanguage();
  }

  @override
  Future<void> setSoundEnabled(bool enabled) async {
    await _hiveService.setSoundEnabled(enabled);
  }

  @override
  bool getSoundEnabled() {
    return _hiveService.getSoundEnabled();
  }

  @override
  Future<void> setVibrationEnabled(bool enabled) async {
    await _hiveService.setVibrationEnabled(enabled);
  }

  @override
  bool getVibrationEnabled() {
    return _hiveService.getVibrationEnabled();
  }

  @override
  Future<void> setNotificationsEnabled(bool enabled) async {
    await _hiveService.setNotificationsEnabled(enabled);
  }

  @override
  bool getNotificationsEnabled() {
    return _hiveService.getNotificationsEnabled();
  }
}
