import '../entities/quiz_result.dart';
import '../repositories/quiz_repository.dart';

class GetQuizResults {
  final QuizRepository _repository;

  GetQuizResults(this._repository);

  List<QuizResult> call() {
    return _repository.getAllQuizResults();
  }
}

class GetQuizResultsByCategory {
  final QuizRepository _repository;

  GetQuizResultsByCategory(this._repository);

  List<QuizResult> call(String category) {
    return _repository.getQuizResultsByCategory(category);
  }
}

class GetQuizResultsByDifficulty {
  final QuizRepository _repository;

  GetQuizResultsByDifficulty(this._repository);

  List<QuizResult> call(String difficulty) {
    return _repository.getQuizResultsByDifficulty(difficulty);
  }
}

class GetBestScore {
  final QuizRepository _repository;

  GetBestScore(this._repository);

  QuizResult? call() {
    return _repository.getBestScore();
  }
}

class GetBestScoreByCategory {
  final QuizRepository _repository;

  GetBestScoreByCategory(this._repository);

  QuizResult? call(String category) {
    return _repository.getBestScoreByCategory(category);
  }
}
