import 'package:just_audio/just_audio.dart';
import 'package:vibration/vibration.dart';
import '../constants/app_constants.dart';
import '../storage/hive_service.dart';

class AudioManager {
  static AudioManager? _instance;
  late AudioPlayer _audioPlayer;
  
  AudioManager._internal() {
    _audioPlayer = AudioPlayer();
  }
  
  static AudioManager get instance {
    _instance ??= AudioManager._internal();
    return _instance!;
  }

  // Play sound effects
  Future<void> playCorrectAnswer() async {
    if (HiveService.instance.getSoundEnabled()) {
      try {
        await _audioPlayer.setAsset(AppConstants.correctAnswerSound);
        await _audioPlayer.play();
      } catch (e) {
        // Handle error silently or log it
        print('Error playing correct answer sound: $e');
      }
    }
  }

  Future<void> playWrongAnswer() async {
    if (HiveService.instance.getSoundEnabled()) {
      try {
        await _audioPlayer.setAsset(AppConstants.wrongAnswerSound);
        await _audioPlayer.play();
      } catch (e) {
        print('Error playing wrong answer sound: $e');
      }
    }
  }

  Future<void> playButtonClick() async {
    if (HiveService.instance.getSoundEnabled()) {
      try {
        await _audioPlayer.setAsset(AppConstants.buttonClickSound);
        await _audioPlayer.play();
      } catch (e) {
        print('Error playing button click sound: $e');
      }
    }
  }

  Future<void> playGameOver() async {
    if (HiveService.instance.getSoundEnabled()) {
      try {
        await _audioPlayer.setAsset(AppConstants.gameOverSound);
        await _audioPlayer.play();
      } catch (e) {
        print('Error playing game over sound: $e');
      }
    }
  }

  // Vibration methods
  Future<void> vibrateCorrect() async {
    if (HiveService.instance.getVibrationEnabled()) {
      try {
        // Short vibration for correct answer
        await Vibration.vibrate(duration: 100);
      } catch (e) {
        print('Error vibrating: $e');
      }
    }
  }

  Future<void> vibrateWrong() async {
    if (HiveService.instance.getVibrationEnabled()) {
      try {
        // Double vibration for wrong answer
        await Vibration.vibrate(duration: 200);
        await Future.delayed(const Duration(milliseconds: 100));
        await Vibration.vibrate(duration: 200);
      } catch (e) {
        print('Error vibrating: $e');
      }
    }
  }

  Future<void> vibrateButton() async {
    if (HiveService.instance.getVibrationEnabled()) {
      try {
        // Very short vibration for button press
        await Vibration.vibrate(duration: 50);
      } catch (e) {
        print('Error vibrating: $e');
      }
    }
  }

  // Combined feedback methods
  Future<void> correctAnswerFeedback() async {
    await Future.wait([
      playCorrectAnswer(),
      vibrateCorrect(),
    ]);
  }

  Future<void> wrongAnswerFeedback() async {
    await Future.wait([
      playWrongAnswer(),
      vibrateWrong(),
    ]);
  }

  Future<void> buttonPressFeedback() async {
    await Future.wait([
      playButtonClick(),
      vibrateButton(),
    ]);
  }

  // Volume control
  Future<void> setVolume(double volume) async {
    await _audioPlayer.setVolume(volume.clamp(0.0, 1.0));
  }

  // Stop all audio
  Future<void> stop() async {
    await _audioPlayer.stop();
  }

  // Dispose
  Future<void> dispose() async {
    await _audioPlayer.dispose();
  }
}
