import '../../domain/entities/question.dart';

class QuestionModel extends Question {
  const QuestionModel({
    required super.category,
    required super.type,
    required super.difficulty,
    required super.question,
    required super.correctAnswer,
    required super.incorrectAnswers,
    required super.allAnswers,
  });

  factory QuestionModel.fromJson(Map<String, dynamic> json) {
    final incorrectAnswers = List<String>.from(json['incorrect_answers'] ?? []);
    final correctAnswer = json['correct_answer'] ?? '';

    // Combine and shuffle answers
    final allAnswers = <String>[...incorrectAnswers, correctAnswer];
    allAnswers.shuffle();

    return QuestionModel(
      category: _decodeHtml(json['category'] ?? ''),
      type: json['type'] ?? '',
      difficulty: json['difficulty'] ?? '',
      question: _decodeHtml(json['question'] ?? ''),
      correctAnswer: _decodeHtml(correctAnswer),
      incorrectAnswers: incorrectAnswers.map<String>((e) => _decodeHtml(e)).toList(),
      allAnswers: allAnswers.map<String>((e) => _decodeHtml(e)).toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'category': category,
      'type': type,
      'difficulty': difficulty,
      'question': question,
      'correct_answer': correctAnswer,
      'incorrect_answers': incorrectAnswers,
    };
  }

  factory QuestionModel.fromEntity(Question question) {
    return QuestionModel(
      category: question.category,
      type: question.type,
      difficulty: question.difficulty,
      question: question.question,
      correctAnswer: question.correctAnswer,
      incorrectAnswers: question.incorrectAnswers,
      allAnswers: question.allAnswers,
    );
  }

  Question toEntity() {
    return Question(
      category: category,
      type: type,
      difficulty: difficulty,
      question: question,
      correctAnswer: correctAnswer,
      incorrectAnswers: incorrectAnswers,
      allAnswers: allAnswers,
    );
  }

  static String _decodeHtml(String htmlString) {
    return htmlString
        .replaceAll('&quot;', '"')
        .replaceAll('&#039;', "'")
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&apos;', "'")
        .replaceAll('&rsquo;', "'")
        .replaceAll('&ldquo;', '"')
        .replaceAll('&rdquo;', '"')
        .replaceAll('&hellip;', '...')
        .replaceAll('&ndash;', '–')
        .replaceAll('&mdash;', '—');
  }
}
