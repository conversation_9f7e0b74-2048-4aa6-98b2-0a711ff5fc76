{"version": 3, "file": "NamedFunctionRange.js", "sourceRoot": "", "sources": ["../../../../../forked/third_party/devtools-frontend/src/extensions/edge_unminification_extension/NamedFunctionRange.ts"], "names": [], "mappings": "AAAA,4DAA4D;AAC5D,qDAAqD;AACrD,yEAAyE;AACzE,6BAA6B;AAO7B,MAAM,OAAO,kBAAkB;IAEhB;IACA;IACA;IAHb,YACa,IAAY,EACZ,KAAe,EACf,GAAa;QAFb,SAAI,GAAJ,IAAI,CAAQ;QACZ,UAAK,GAAL,KAAK,CAAU;QACf,QAAG,GAAH,GAAG,CAAU;QAExB,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzE,MAAM,IAAI,KAAK,CACX,wEAAwE,KAAK,CAAC,IAAI,iBAC9E,KAAK,CAAC,MAAM,aAAa,GAAG,CAAC,IAAI,eAAe,GAAG,CAAC,MAAM,EAAE,CACnE,CAAC;QACJ,CAAC;QACD,IAAI,KAAK,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;YACpF,MAAM,IAAI,KAAK,CACX,iEAAiE,KAAK,CAAC,IAAI,iBACvE,KAAK,CAAC,MAAM,aAAa,GAAG,CAAC,IAAI,eAAe,GAAG,CAAC,MAAM,EAAE,CACnE,CAAC;QACJ,CAAC;IACH,CAAC;CACF", "sourcesContent": ["// Copyright 2025 The Chromium Authors. All rights reserved.\n// Copyright (C) Microsoft Corp. All rights reserved.\n// Use of this source code is governed by a BSD-style license that can be\n// found in the LICENSE file.\n\ninterface Position {\n  line: number;\n  column: number;\n}\n\nexport class NamedFunctionRange {\n  constructor(\n      readonly name: string,\n      readonly start: Position,\n      readonly end: Position,\n  ) {\n    if (start.line < 0 || start.column < 0 || end.line < 0 || end.column < 0) {\n      throw new Error(\n          `Line and column positions should be positive but were not: startLine=${start.line}, startColumn=${\n              start.column}, endLine=${end.line}, endColumn=${end.column}`,\n      );\n    }\n    if (start.line > end.line || (start.line === end.line && start.column > end.column)) {\n      throw new Error(\n          `End position should be greater than start position: startLine=${start.line}, startColumn=${\n              start.column}, endLine=${end.line}, endColumn=${end.column}`,\n      );\n    }\n  }\n}\n"]}