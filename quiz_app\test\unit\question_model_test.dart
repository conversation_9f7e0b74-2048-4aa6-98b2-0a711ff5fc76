import 'package:flutter_test/flutter_test.dart';
import 'package:quiz_app/data/models/question_model.dart';
import 'package:quiz_app/domain/entities/question.dart';

void main() {
  group('QuestionModel', () {
    test('should create QuestionModel from JSON correctly', () {
      // Arrange
      final json = {
        'category': 'Science',
        'type': 'multiple',
        'difficulty': 'easy',
        'question': 'What is the chemical symbol for water?',
        'correct_answer': 'H2O',
        'incorrect_answers': ['CO2', 'NaCl', 'O2'],
      };

      // Act
      final questionModel = QuestionModel.fromJson(json);

      // Assert
      expect(questionModel.category, 'Science');
      expect(questionModel.type, 'multiple');
      expect(questionModel.difficulty, 'easy');
      expect(questionModel.question, 'What is the chemical symbol for water?');
      expect(questionModel.correctAnswer, 'H2O');
      expect(questionModel.incorrectAnswers, ['CO2', 'NaCl', 'O2']);
      expect(questionModel.allAnswers.length, 4);
      expect(questionModel.allAnswers.contains('H2O'), true);
      expect(questionModel.allAnswers.contains('CO2'), true);
    });

    test('should decode HTML entities correctly', () {
      // Arrange
      final json = {
        'category': 'Entertainment',
        'type': 'multiple',
        'difficulty': 'medium',
        'question': 'What&#039;s the name of Batman&#039;s butler?',
        'correct_answer': 'Alfred',
        'incorrect_answers': ['Winston', 'Geoffrey', 'Jeeves'],
      };

      // Act
      final questionModel = QuestionModel.fromJson(json);

      // Assert
      expect(questionModel.question, "What's the name of Batman's butler?");
    });

    test('should convert to JSON correctly', () {
      // Arrange
      final questionModel = QuestionModel(
        category: 'Science',
        type: 'multiple',
        difficulty: 'easy',
        question: 'What is the chemical symbol for water?',
        correctAnswer: 'H2O',
        incorrectAnswers: ['CO2', 'NaCl', 'O2'],
        allAnswers: ['H2O', 'CO2', 'NaCl', 'O2'],
      );

      // Act
      final json = questionModel.toJson();

      // Assert
      expect(json['category'], 'Science');
      expect(json['type'], 'multiple');
      expect(json['difficulty'], 'easy');
      expect(json['question'], 'What is the chemical symbol for water?');
      expect(json['correct_answer'], 'H2O');
      expect(json['incorrect_answers'], ['CO2', 'NaCl', 'O2']);
    });

    test('should convert to entity correctly', () {
      // Arrange
      final questionModel = QuestionModel(
        category: 'Science',
        type: 'multiple',
        difficulty: 'easy',
        question: 'What is the chemical symbol for water?',
        correctAnswer: 'H2O',
        incorrectAnswers: ['CO2', 'NaCl', 'O2'],
        allAnswers: ['H2O', 'CO2', 'NaCl', 'O2'],
      );

      // Act
      final entity = questionModel.toEntity();

      // Assert
      expect(entity, isA<Question>());
      expect(entity.category, 'Science');
      expect(entity.correctAnswer, 'H2O');
    });

    test('should create from entity correctly', () {
      // Arrange
      final entity = Question(
        category: 'Science',
        type: 'multiple',
        difficulty: 'easy',
        question: 'What is the chemical symbol for water?',
        correctAnswer: 'H2O',
        incorrectAnswers: ['CO2', 'NaCl', 'O2'],
        allAnswers: ['H2O', 'CO2', 'NaCl', 'O2'],
      );

      // Act
      final questionModel = QuestionModel.fromEntity(entity);

      // Assert
      expect(questionModel.category, 'Science');
      expect(questionModel.correctAnswer, 'H2O');
    });
  });
}
