import 'package:equatable/equatable.dart';

class Question extends Equatable {
  final String category;
  final String type;
  final String difficulty;
  final String question;
  final String correctAnswer;
  final List<String> incorrectAnswers;
  final List<String> allAnswers;

  const Question({
    required this.category,
    required this.type,
    required this.difficulty,
    required this.question,
    required this.correctAnswer,
    required this.incorrectAnswers,
    required this.allAnswers,
  });

  @override
  List<Object?> get props => [
        category,
        type,
        difficulty,
        question,
        correctAnswer,
        incorrectAnswers,
        allAnswers,
      ];

  Question copyWith({
    String? category,
    String? type,
    String? difficulty,
    String? question,
    String? correctAnswer,
    List<String>? incorrectAnswers,
    List<String>? allAnswers,
  }) {
    return Question(
      category: category ?? this.category,
      type: type ?? this.type,
      difficulty: difficulty ?? this.difficulty,
      question: question ?? this.question,
      correctAnswer: correctAnswer ?? this.correctAnswer,
      incorrectAnswers: incorrectAnswers ?? this.incorrectAnswers,
      allAnswers: allAnswers ?? this.allAnswers,
    );
  }
}
