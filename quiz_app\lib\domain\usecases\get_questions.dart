import '../entities/question.dart';
import '../repositories/quiz_repository.dart';

class GetQuestions {
  final QuizRepository _repository;

  GetQuestions(this._repository);

  Future<List<Question>> call(GetQuestionsParams params) async {
    return await _repository.getQuestions(
      amount: params.amount,
      category: params.category,
      difficulty: params.difficulty,
      type: params.type,
    );
  }
}

class GetQuestionsParams {
  final int amount;
  final int? category;
  final String? difficulty;
  final String? type;

  GetQuestionsParams({
    required this.amount,
    this.category,
    this.difficulty,
    this.type,
  });
}
